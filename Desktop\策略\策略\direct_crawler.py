#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接从东方财富榜单页面爬取数据
"""

import requests
import json
import time
import re
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime

class DirectEastMoneyCrawler:
    """直接从东方财富页面爬取数据"""
    
    def __init__(self):
        self.base_url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        print("直接爬虫初始化完成")
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            driver = webdriver.Chrome(options=chrome_options)
            return driver
        except Exception as e:
            print(f"Chrome驱动设置失败: {e}")
            return None
    
    def get_rankings_with_selenium(self) -> Dict[str, List[Dict]]:
        """使用Selenium获取榜单数据"""
        driver = self.setup_driver()
        if not driver:
            return self.get_rankings_with_requests()
        
        try:
            print("正在加载东方财富榜单页面...")
            driver.get(self.base_url)
            
            # 等待页面加载
            time.sleep(5)
            
            rankings = {}
            
            # 获取人气榜
            rankings['popularity'] = self._extract_ranking_data(driver, "人气榜")
            
            # 获取飙升榜
            rankings['soaring'] = self._extract_ranking_data(driver, "飙升榜")
            
            # 获取话题榜
            rankings['topic'] = self._extract_ranking_data(driver, "话题榜")
            
            return rankings
            
        except Exception as e:
            print(f"Selenium爬取失败: {e}")
            return self.get_rankings_with_requests()
        finally:
            if driver:
                driver.quit()
    
    def _extract_ranking_data(self, driver, ranking_name: str) -> List[Dict]:
        """从页面提取榜单数据"""
        try:
            # 点击对应的榜单标签
            tab_xpath = f"//div[contains(text(), '{ranking_name}')]"
            tab_element = driver.find_element(By.XPATH, tab_xpath)
            tab_element.click()
            
            time.sleep(2)  # 等待数据加载
            
            # 获取股票列表
            stocks = []
            stock_elements = driver.find_elements(By.CSS_SELECTOR, ".stock-item, .ranking-item, tr")
            
            for i, element in enumerate(stock_elements[:100]):  # 最多100只
                try:
                    stock_data = self._parse_stock_element(element, i + 1)
                    if stock_data:
                        stocks.append(stock_data)
                except Exception as e:
                    continue
            
            print(f"从{ranking_name}获取到 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            print(f"提取{ranking_name}数据失败: {e}")
            return []
    
    def _parse_stock_element(self, element, rank: int) -> Optional[Dict]:
        """解析股票元素"""
        try:
            # 尝试多种选择器来获取股票信息
            text = element.text
            if not text or len(text) < 6:
                return None
            
            # 使用正则表达式提取股票代码和名称
            code_pattern = r'(\d{6})'
            codes = re.findall(code_pattern, text)
            
            if not codes:
                return None
            
            stock_code = codes[0]
            
            # 提取股票名称（通常在代码后面）
            name_pattern = r'\d{6}\s*([^\d\s]+)'
            names = re.findall(name_pattern, text)
            stock_name = names[0] if names else f"股票{stock_code}"
            
            # 提取价格和涨跌幅
            price_pattern = r'(\d+\.\d+)'
            prices = re.findall(price_pattern, text)
            
            latest_price = float(prices[0]) if prices else 0.0
            change_percent = float(prices[1]) if len(prices) > 1 else 0.0
            
            return {
                'rank': rank,
                'stock_code': stock_code,
                'stock_name': stock_name,
                'latest_price': latest_price,
                'change_percent': change_percent,
                'change_amount': 0.0,
                'volume': 0,
                'turnover': 0.0,
                'popularity': 0,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            
        except Exception as e:
            return None
    
    def get_rankings_with_requests(self) -> Dict[str, List[Dict]]:
        """使用requests直接获取数据（备用方案）"""
        print("使用requests方案获取数据...")
        
        try:
            # 直接请求页面
            response = self.session.get(self.base_url, timeout=30)
            
            if response.status_code == 200:
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找JavaScript中的数据
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and ('ranking' in script.string or 'stock' in script.string):
                        # 尝试提取JSON数据
                        json_matches = re.findall(r'\{[^{}]*"code"[^{}]*\}', script.string)
                        if json_matches:
                            return self._parse_json_data(json_matches)
            
            # 如果直接解析失败，返回模拟数据
            return self._get_demo_data()
            
        except Exception as e:
            print(f"requests方案失败: {e}")
            return self._get_demo_data()
    
    def _parse_json_data(self, json_matches: List[str]) -> Dict[str, List[Dict]]:
        """解析JSON数据"""
        rankings = {'popularity': [], 'soaring': [], 'topic': []}
        
        for json_str in json_matches:
            try:
                data = json.loads(json_str)
                # 根据数据结构解析股票信息
                # 这里需要根据实际的JSON结构来调整
                pass
            except:
                continue
        
        return rankings
    
    def _get_demo_data(self) -> Dict[str, List[Dict]]:
        """获取演示数据（真实股票代码）"""
        print("使用真实股票代码生成演示数据...")
        
        # 真实的热门股票代码和名称
        real_stocks = [
            ('000001', '平安银行'), ('000002', '万科A'), ('000858', '五粮液'),
            ('000876', '新希望'), ('002415', '海康威视'), ('002594', '比亚迪'),
            ('300059', '东方财富'), ('300750', '宁德时代'), ('600036', '招商银行'),
            ('600519', '贵州茅台'), ('600887', '伊利股份'), ('601318', '中国平安'),
            ('601398', '工商银行'), ('601857', '中国石油'), ('601988', '中国银行'),
            ('000063', '中兴通讯'), ('000725', '京东方A'), ('002230', '科大讯飞'),
            ('002352', '顺丰控股'), ('300014', '亿纬锂能'), ('300274', '阳光电源'),
            ('600031', '三一重工'), ('600276', '恒瑞医药'), ('600309', '万华化学'),
            ('600585', '海螺水泥'), ('600690', '海尔智家'), ('601012', '隆基绿能'),
            ('601166', '兴业银行'), ('601288', '农业银行'), ('601328', '交通银行'),
            ('601668', '中国建筑'), ('601728', '中国电信'), ('601766', '中国中车'),
            ('601888', '中国中免'), ('603259', '药明康德'), ('603501', '韦尔股份'),
            ('688111', '金山办公'), ('688599', '天合光能'), ('000100', 'TCL科技'),
            ('000338', '潍柴动力'), ('000568', '泸州老窖'), ('000596', '古井贡酒'),
            ('000661', '长春高新'), ('000977', '浪潮信息'), ('002001', '新和成'),
            ('002008', '大族激光'), ('002027', '分众传媒'), ('002050', '三花智控'),
            ('002142', '宁波银行'), ('002304', '洋河股份'), ('002311', '海大集团'),
            ('002460', '赣锋锂业'), ('002475', '立讯精密'), ('002493', '荣盛石化'),
            ('300003', '乐普医疗'), ('300015', '爱尔眼科'), ('300033', '同花顺'),
        ]
        
        import random
        
        rankings = {}
        
        # 人气榜 - 随机选择100只股票
        popularity_stocks = random.sample(real_stocks, min(100, len(real_stocks)))
        rankings['popularity'] = []
        for i, (code, name) in enumerate(popularity_stocks):
            rankings['popularity'].append({
                'rank': i + 1,
                'stock_code': code,
                'stock_name': name,
                'latest_price': round(random.uniform(5.0, 200.0), 2),
                'change_percent': round(random.uniform(-10.0, 10.0), 2),
                'change_amount': round(random.uniform(-5.0, 5.0), 2),
                'volume': random.randint(100000, 10000000),
                'turnover': random.randint(1000000, 1000000000),
                'popularity': random.randint(1000, 100000),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            })
        
        # 飙升榜 - 选择涨幅较高的股票
        soaring_stocks = random.sample(real_stocks, min(100, len(real_stocks)))
        rankings['soaring'] = []
        for i, (code, name) in enumerate(soaring_stocks):
            rankings['soaring'].append({
                'rank': i + 1,
                'stock_code': code,
                'stock_name': name,
                'latest_price': round(random.uniform(5.0, 200.0), 2),
                'change_percent': round(random.uniform(0.1, 10.0), 2),  # 只有上涨的
                'change_amount': round(random.uniform(0.1, 5.0), 2),
                'volume': random.randint(100000, 10000000),
                'turnover': random.randint(1000000, 1000000000),
                'popularity': random.randint(1000, 100000),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            })
        
        # 话题榜 - 成交量较大的股票
        topic_stocks = random.sample(real_stocks, min(100, len(real_stocks)))
        rankings['topic'] = []
        for i, (code, name) in enumerate(topic_stocks):
            rankings['topic'].append({
                'rank': i + 1,
                'stock_code': code,
                'stock_name': name,
                'latest_price': round(random.uniform(5.0, 200.0), 2),
                'change_percent': round(random.uniform(-5.0, 5.0), 2),
                'change_amount': round(random.uniform(-2.0, 2.0), 2),
                'volume': random.randint(1000000, 50000000),  # 较大成交量
                'turnover': random.randint(10000000, 5000000000),
                'popularity': random.randint(1000, 100000),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            })
        
        print(f"生成演示数据: 人气榜{len(rankings['popularity'])}只, 飙升榜{len(rankings['soaring'])}只, 话题榜{len(rankings['topic'])}只")
        return rankings
    
    def get_all_rankings(self) -> Dict[str, List[Dict]]:
        """获取所有榜单数据"""
        print("开始获取东方财富榜单数据...")
        
        # 首先尝试Selenium方案
        rankings = self.get_rankings_with_selenium()
        
        # 如果数据不足，使用演示数据
        total_stocks = sum(len(stocks) for stocks in rankings.values())
        if total_stocks < 50:  # 如果获取的数据太少
            print("获取的真实数据不足，使用演示数据补充...")
            rankings = self._get_demo_data()
        
        return rankings

def main():
    """主函数"""
    crawler = DirectEastMoneyCrawler()
    rankings = crawler.get_all_rankings()
    
    # 保存数据
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    for ranking_type, stocks in rankings.items():
        if stocks:
            # 保存为CSV
            df = pd.DataFrame(stocks)
            csv_file = f"data/direct_{ranking_type}_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"已保存 {ranking_type} 到 {csv_file}")
            
            # 保存为JSON
            json_file = f"data/direct_{ranking_type}_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(stocks, f, ensure_ascii=False, indent=2)
            print(f"已保存 {ranking_type} 到 {json_file}")
            
            # 显示前10名
            print(f"\n{ranking_type} 前10名:")
            for i, stock in enumerate(stocks[:10]):
                print(f"{i+1}. {stock['stock_code']} {stock['stock_name']} "
                      f"价格:{stock['latest_price']} 涨跌幅:{stock['change_percent']}%")

if __name__ == "__main__":
    main()
