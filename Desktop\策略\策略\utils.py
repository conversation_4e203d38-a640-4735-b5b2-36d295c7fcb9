#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import os
import json
import csv
import logging
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

from config import LOGGING_CONFIG, DATA_DIR, LOGS_DIR

def setup_logging():
    """设置日志配置"""
    log_file = os.path.join(LOGS_DIR, f"eastmoney_crawler_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def clean_jsonp_response(response_text: str) -> str:
    """清理JSONP响应，提取JSON数据"""
    try:
        # 移除JSONP包装
        if response_text.startswith('jQuery'):
            start = response_text.find('(') + 1
            end = response_text.rfind(')')
            return response_text[start:end]
        return response_text
    except Exception as e:
        logging.error(f"清理JSONP响应失败: {e}")
        return response_text

def parse_stock_data(raw_data: Dict) -> List[Dict]:
    """解析股票数据"""
    try:
        if 'data' not in raw_data or 'diff' not in raw_data['data']:
            return []
        
        stocks = []
        for item in raw_data['data']['diff']:
            stock = {
                'stock_code': item.get('f12', ''),
                'stock_name': item.get('f14', ''),
                'latest_price': item.get('f2', 0),
                'change_percent': item.get('f3', 0),
                'change_amount': item.get('f4', 0),
                'volume': item.get('f5', 0),
                'turnover': item.get('f6', 0),
                'amplitude': item.get('f7', 0),
                'turnover_rate': item.get('f8', 0),
                'pe_ratio': item.get('f9', 0),
                'volume_ratio': item.get('f10', 0),
                'highest_price': item.get('f15', 0),
                'lowest_price': item.get('f16', 0),
                'open_price': item.get('f17', 0),
                'prev_close': item.get('f18', 0),
                'total_market_cap': item.get('f20', 0),
                'circulating_market_cap': item.get('f21', 0),
                'speed': item.get('f22', 0),
                'pb_ratio': item.get('f23', 0),
                'popularity': item.get('f62', 0),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            stocks.append(stock)
        
        return stocks
    except Exception as e:
        logging.error(f"解析股票数据失败: {e}")
        return []

def save_to_csv(data: List[Dict], filename: str) -> bool:
    """保存数据到CSV文件"""
    try:
        if not data:
            logging.warning("没有数据需要保存")
            return False
        
        filepath = os.path.join(DATA_DIR, filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            if data:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
        
        logging.info(f"数据已保存到CSV文件: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"保存CSV文件失败: {e}")
        return False

def save_to_json(data: List[Dict], filename: str) -> bool:
    """保存数据到JSON文件"""
    try:
        filepath = os.path.join(DATA_DIR, filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        logging.info(f"数据已保存到JSON文件: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"保存JSON文件失败: {e}")
        return False

def load_from_json(filename: str) -> List[Dict]:
    """从JSON文件加载数据"""
    try:
        filepath = os.path.join(DATA_DIR, filename)
        if not os.path.exists(filepath):
            return []
        
        with open(filepath, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
        
        return data if isinstance(data, list) else []
        
    except Exception as e:
        logging.error(f"加载JSON文件失败: {e}")
        return []

def update_history(ranking_type: str, current_data: List[Dict]) -> bool:
    """更新历史数据"""
    try:
        from config import get_history_filename
        
        history_file = get_history_filename(ranking_type)
        history_data = load_from_json(history_file)
        
        # 添加当前时间戳
        current_entry = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data': current_data
        }
        
        history_data.append(current_entry)
        
        # 保留最近30天的数据
        cutoff_date = datetime.now() - timedelta(days=30)
        history_data = [
            entry for entry in history_data
            if datetime.strptime(entry['timestamp'], '%Y-%m-%d %H:%M:%S') > cutoff_date
        ]
        
        return save_to_json(history_data, history_file)
        
    except Exception as e:
        logging.error(f"更新历史数据失败: {e}")
        return False

def is_market_open() -> bool:
    """检查是否在交易时间内"""
    try:
        from config import SCHEDULE_CONFIG
        
        now = datetime.now()
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        current_time = now.strftime('%H:%M')
        market_hours = SCHEDULE_CONFIG['market_hours']
        
        # 上午交易时间
        morning_start = market_hours['start']
        morning_end = market_hours['lunch_start']
        
        # 下午交易时间
        afternoon_start = market_hours['lunch_end']
        afternoon_end = market_hours['end']
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)
               
    except Exception as e:
        logging.error(f"检查交易时间失败: {e}")
        return True  # 出错时默认允许更新

def format_number(value: Any) -> str:
    """格式化数字显示"""
    try:
        if value is None or value == '':
            return '0'
        
        num = float(value)
        
        if abs(num) >= 100000000:  # 亿
            return f"{num/100000000:.2f}亿"
        elif abs(num) >= 10000:    # 万
            return f"{num/10000:.2f}万"
        else:
            return f"{num:.2f}"
            
    except (ValueError, TypeError):
        return str(value)

def validate_stock_code(code: str) -> bool:
    """验证股票代码格式"""
    if not code:
        return False
    
    # A股股票代码格式: 6位数字
    pattern = r'^\d{6}$'
    return bool(re.match(pattern, code))

def get_file_size(filepath: str) -> int:
    """获取文件大小(字节)"""
    try:
        return os.path.getsize(filepath)
    except OSError:
        return 0

def cleanup_old_files(days: int = 30):
    """清理旧文件"""
    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        
        for root, dirs, files in os.walk(DATA_DIR):
            for file in files:
                filepath = os.path.join(root, file)
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                
                if file_time < cutoff_date:
                    os.remove(filepath)
                    logging.info(f"删除旧文件: {filepath}")
                    
    except Exception as e:
        logging.error(f"清理旧文件失败: {e}")

def create_summary_report(data: List[Dict]) -> Dict:
    """创建数据摘要报告"""
    try:
        if not data:
            return {}
        
        df = pd.DataFrame(data)
        
        summary = {
            'total_stocks': len(data),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'top_gainers': df.nlargest(5, 'change_percent')[['stock_code', 'stock_name', 'change_percent']].to_dict('records'),
            'top_losers': df.nsmallest(5, 'change_percent')[['stock_code', 'stock_name', 'change_percent']].to_dict('records'),
            'most_popular': df.nlargest(5, 'popularity')[['stock_code', 'stock_name', 'popularity']].to_dict('records'),
            'highest_volume': df.nlargest(5, 'volume')[['stock_code', 'stock_name', 'volume']].to_dict('records'),
            'statistics': {
                'avg_change_percent': df['change_percent'].mean(),
                'total_volume': df['volume'].sum(),
                'total_turnover': df['turnover'].sum(),
            }
        }
        
        return summary
        
    except Exception as e:
        logging.error(f"创建摘要报告失败: {e}")
        return {}

# 初始化日志
logger = setup_logging()
