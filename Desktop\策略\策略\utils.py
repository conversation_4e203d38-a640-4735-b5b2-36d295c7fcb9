#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import os
import json
import csv
import logging
import time
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

from config import LOGGING_CONFIG, DATA_DIR, LOGS_DIR

def setup_logging():
    """设置日志配置"""
    log_file = os.path.join(LOGS_DIR, f"eastmoney_crawler_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['level']),
        format=LOGGING_CONFIG['format'],
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def clean_jsonp_response(response_text: str) -> str:
    """清理JSONP响应，提取JSON数据"""
    try:
        # 移除JSONP包装
        if response_text.startswith('jQuery'):
            start = response_text.find('(') + 1
            end = response_text.rfind(')')
            return response_text[start:end]
        return response_text
    except Exception as e:
        logging.error(f"清理JSONP响应失败: {e}")
        return response_text

def parse_stock_data(raw_data: Dict) -> List[Dict]:
    """解析股票数据"""
    try:
        if 'data' not in raw_data or 'diff' not in raw_data['data']:
            return []

        stocks = []
        for item in raw_data['data']['diff']:
            # 确保数值类型正确
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value is not None else default
                except (ValueError, TypeError):
                    return default

            def safe_int(value, default=0):
                try:
                    return int(value) if value is not None else default
                except (ValueError, TypeError):
                    return default

            stock = {
                'stock_code': str(item.get('f12', '')),
                'stock_name': str(item.get('f14', '')),
                'latest_price': safe_float(item.get('f2')),
                'change_percent': safe_float(item.get('f3')),
                'change_amount': safe_float(item.get('f4')),
                'volume': safe_int(item.get('f5')),
                'turnover': safe_float(item.get('f6')),
                'amplitude': safe_float(item.get('f7')),
                'turnover_rate': safe_float(item.get('f8')),
                'pe_ratio': safe_float(item.get('f9')),
                'volume_ratio': safe_float(item.get('f10')),
                'highest_price': safe_float(item.get('f15')),
                'lowest_price': safe_float(item.get('f16')),
                'open_price': safe_float(item.get('f17')),
                'prev_close': safe_float(item.get('f18')),
                'total_market_cap': safe_float(item.get('f20')),
                'circulating_market_cap': safe_float(item.get('f21')),
                'speed': safe_float(item.get('f22')),
                'pb_ratio': safe_float(item.get('f23')),
                'popularity': safe_int(item.get('f62')),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            stocks.append(stock)

        return stocks
    except Exception as e:
        logging.error(f"解析股票数据失败: {e}")
        return []

def save_to_csv(data: List[Dict], filename: str) -> bool:
    """保存数据到CSV文件"""
    try:
        if not data:
            logging.warning("没有数据需要保存")
            return False
        
        filepath = os.path.join(DATA_DIR, filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 写入CSV文件
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            if data:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
        
        logging.info(f"数据已保存到CSV文件: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"保存CSV文件失败: {e}")
        return False

def save_to_json(data: List[Dict], filename: str) -> bool:
    """保存数据到JSON文件"""
    try:
        filepath = os.path.join(DATA_DIR, filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2)
        
        logging.info(f"数据已保存到JSON文件: {filepath}")
        return True
        
    except Exception as e:
        logging.error(f"保存JSON文件失败: {e}")
        return False

def load_from_json(filename: str) -> List[Dict]:
    """从JSON文件加载数据"""
    try:
        filepath = os.path.join(DATA_DIR, filename)
        if not os.path.exists(filepath):
            return []
        
        with open(filepath, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
        
        return data if isinstance(data, list) else []
        
    except Exception as e:
        logging.error(f"加载JSON文件失败: {e}")
        return []

def update_history(ranking_type: str, current_data: List[Dict]) -> bool:
    """更新历史数据"""
    try:
        from config import get_history_filename
        
        history_file = get_history_filename(ranking_type)
        history_data = load_from_json(history_file)
        
        # 添加当前时间戳
        current_entry = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data': current_data
        }
        
        history_data.append(current_entry)
        
        # 保留最近30天的数据
        cutoff_date = datetime.now() - timedelta(days=30)
        history_data = [
            entry for entry in history_data
            if datetime.strptime(entry['timestamp'], '%Y-%m-%d %H:%M:%S') > cutoff_date
        ]
        
        return save_to_json(history_data, history_file)
        
    except Exception as e:
        logging.error(f"更新历史数据失败: {e}")
        return False

def is_market_open() -> bool:
    """检查是否在交易时间内"""
    try:
        from config import SCHEDULE_CONFIG
        
        now = datetime.now()
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        current_time = now.strftime('%H:%M')
        market_hours = SCHEDULE_CONFIG['market_hours']
        
        # 上午交易时间
        morning_start = market_hours['start']
        morning_end = market_hours['lunch_start']
        
        # 下午交易时间
        afternoon_start = market_hours['lunch_end']
        afternoon_end = market_hours['end']
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)
               
    except Exception as e:
        logging.error(f"检查交易时间失败: {e}")
        return True  # 出错时默认允许更新

def format_number(value: Any) -> str:
    """格式化数字显示"""
    try:
        if value is None or value == '':
            return '0'
        
        num = float(value)
        
        if abs(num) >= 100000000:  # 亿
            return f"{num/100000000:.2f}亿"
        elif abs(num) >= 10000:    # 万
            return f"{num/10000:.2f}万"
        else:
            return f"{num:.2f}"
            
    except (ValueError, TypeError):
        return str(value)

def validate_stock_code(code: str) -> bool:
    """验证股票代码格式"""
    if not code:
        return False

    # A股股票代码格式: 6位数字
    pattern = r'^\d{6}$'
    return bool(re.match(pattern, code))

def is_valid_stock(stock: Dict) -> bool:
    """验证股票是否为有效的活跃股票"""
    try:
        # 检查股票代码格式
        code = stock.get('stock_code', '')
        if not validate_stock_code(code):
            return False

        # 检查股票名称，排除退市股票
        name = stock.get('stock_name', '')
        if not name:
            return False

        # 排除退市股票标识
        invalid_prefixes = ['PT', '*ST', 'ST', '退', 'N']
        invalid_suffixes = ['退', 'A退', 'B退']

        for prefix in invalid_prefixes:
            if name.startswith(prefix):
                return False

        for suffix in invalid_suffixes:
            if name.endswith(suffix):
                return False

        # 检查价格，排除价格为0的股票
        price = float(stock.get('latest_price', 0))
        if price <= 0:
            return False

        # 检查是否有基本的交易数据
        volume = int(stock.get('volume', 0))
        if volume <= 0:
            return False

        return True

    except (ValueError, TypeError):
        return False

def filter_valid_stocks(stocks: List[Dict]) -> List[Dict]:
    """过滤出有效的活跃股票"""
    try:
        valid_stocks = [stock for stock in stocks if is_valid_stock(stock)]
        logger.info(f"过滤前: {len(stocks)} 只股票, 过滤后: {len(valid_stocks)} 只有效股票")
        return valid_stocks
    except Exception as e:
        logger.error(f"过滤股票数据失败: {e}")
        return stocks

def get_file_size(filepath: str) -> int:
    """获取文件大小(字节)"""
    try:
        return os.path.getsize(filepath)
    except OSError:
        return 0

def cleanup_old_files(days: int = 30):
    """清理旧文件"""
    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        
        for root, dirs, files in os.walk(DATA_DIR):
            for file in files:
                filepath = os.path.join(root, file)
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                
                if file_time < cutoff_date:
                    os.remove(filepath)
                    logging.info(f"删除旧文件: {filepath}")
                    
    except Exception as e:
        logging.error(f"清理旧文件失败: {e}")

def create_summary_report(data: List[Dict]) -> Dict:
    """创建数据摘要报告"""
    try:
        if not data:
            return {}

        df = pd.DataFrame(data)

        # 确保数值列的数据类型正确
        numeric_columns = ['change_percent', 'popularity', 'volume', 'turnover']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        def convert_to_python_types(records):
            """转换numpy类型为Python原生类型"""
            result = []
            for record in records:
                converted = {}
                for key, value in record.items():
                    if hasattr(value, 'item'):  # numpy类型
                        converted[key] = value.item()
                    else:
                        converted[key] = value
                result.append(converted)
            return result

        summary = {
            'total_stocks': len(data),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'top_gainers': convert_to_python_types(df.nlargest(5, 'change_percent')[['stock_code', 'stock_name', 'change_percent']].to_dict('records')),
            'top_losers': convert_to_python_types(df.nsmallest(5, 'change_percent')[['stock_code', 'stock_name', 'change_percent']].to_dict('records')),
            'most_popular': convert_to_python_types(df.nlargest(5, 'popularity')[['stock_code', 'stock_name', 'popularity']].to_dict('records')),
            'highest_volume': convert_to_python_types(df.nlargest(5, 'volume')[['stock_code', 'stock_name', 'volume']].to_dict('records')),
            'statistics': {
                'avg_change_percent': float(df['change_percent'].mean()),
                'total_volume': int(df['volume'].sum()),
                'total_turnover': float(df['turnover'].sum()),
            }
        }

        return summary

    except Exception as e:
        logging.error(f"创建摘要报告失败: {e}")
        return {}

# 初始化日志
logger = setup_logging()
