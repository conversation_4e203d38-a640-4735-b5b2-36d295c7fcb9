#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单爬虫启动脚本
"""

import sys
import os
import argparse
import threading
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import EastMoneyRankingApp
from web_app import run_web_app
from scheduler import start_scheduler, stop_scheduler
from utils import logger

def start_web_mode(host='127.0.0.1', port=5000):
    """启动Web模式"""
    try:
        logger.info("启动Web模式")
        
        # 启动调度器
        start_scheduler()
        
        # 启动Web应用
        run_web_app(host=host, port=port, debug=False)
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭...")
    except Exception as e:
        logger.error(f"Web模式启动失败: {e}")
    finally:
        stop_scheduler()

def start_daemon_mode():
    """启动守护进程模式"""
    try:
        logger.info("启动守护进程模式")
        app = EastMoneyRankingApp()
        app.start_daemon()
    except Exception as e:
        logger.error(f"守护进程模式启动失败: {e}")

def start_mixed_mode(host='127.0.0.1', port=5000):
    """启动混合模式（Web + 守护进程）"""
    try:
        logger.info("启动混合模式")
        
        # 在单独线程中启动调度器
        scheduler_thread = threading.Thread(target=start_scheduler, daemon=True)
        scheduler_thread.start()
        
        # 等待调度器启动
        time.sleep(2)
        
        # 启动Web应用
        run_web_app(host=host, port=port, debug=False)
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭...")
    except Exception as e:
        logger.error(f"混合模式启动失败: {e}")
    finally:
        stop_scheduler()

def quick_fetch():
    """快速获取一次数据"""
    try:
        logger.info("快速获取数据")
        app = EastMoneyRankingApp()
        success = app.fetch_once()
        
        if success:
            print("\n数据获取成功！")
            app.show_latest_data()
        else:
            print("数据获取失败！")
            
    except Exception as e:
        logger.error(f"快速获取失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
东方财富榜单爬虫 - 使用说明

运行模式：
  web      - Web界面模式（推荐）
  daemon   - 守护进程模式（后台运行）
  mixed    - 混合模式（Web + 守护进程）
  fetch    - 快速获取一次数据
  help     - 显示此帮助信息

使用示例：
  python start.py web                    # 启动Web界面
  python start.py web --host 0.0.0.0    # 启动Web界面，允许外部访问
  python start.py daemon                # 启动守护进程
  python start.py fetch                 # 快速获取数据
  
Web界面功能：
  - 实时查看三个榜单数据
  - 自动定时更新
  - 数据导出功能
  - 统计信息查看
  
命令行功能：
  python main.py --mode once            # 获取一次数据
  python main.py --mode daemon          # 守护进程模式
  python main.py --mode show            # 显示最新数据
  python main.py --mode stats           # 显示统计信息
  python main.py --mode export          # 导出数据
  
数据文件位置：
  - CSV文件：data/目录下
  - JSON文件：data/目录下
  - 数据库：data/rankings.db
  - 日志文件：logs/目录下
  
注意事项：
  1. 首次运行需要安装依赖：pip install -r requirements.txt
  2. 数据更新间隔默认为5分钟，可在config.py中修改
  3. 只在交易时间内自动更新数据
  4. 数据会自动保存为CSV、JSON和数据库格式
"""
    print(help_text)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='东方财富榜单爬虫启动器')
    parser.add_argument('mode', nargs='?', default='web',
                       choices=['web', 'daemon', 'mixed', 'fetch', 'help'],
                       help='运行模式')
    parser.add_argument('--host', default='127.0.0.1', help='Web服务器地址')
    parser.add_argument('--port', type=int, default=5000, help='Web服务器端口')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'web':
            start_web_mode(args.host, args.port)
        elif args.mode == 'daemon':
            start_daemon_mode()
        elif args.mode == 'mixed':
            start_mixed_mode(args.host, args.port)
        elif args.mode == 'fetch':
            quick_fetch()
        elif args.mode == 'help':
            show_help()
        else:
            show_help()
            
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
