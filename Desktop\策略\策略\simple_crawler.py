#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版东方财富榜单爬虫 - 直接获取真实数据
"""

import requests
import json
import pandas as pd
import os
from datetime import datetime
import random
import time

class SimpleEastMoneyCrawler:
    """简化版爬虫，直接获取真实股票数据"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://vipmoney.eastmoney.com/',
        })
        
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        
        print("简化版爬虫初始化完成")
    
    def get_real_stock_data(self) -> dict:
        """获取真实的股票数据"""
        print("正在获取真实股票数据...")
        
        # 东方财富API - 获取沪深A股数据
        api_urls = [
            # 人气榜 - 按关注度排序
            "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=0&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f62&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152",
            
            # 涨幅榜 - 按涨跌幅排序
            "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=0&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152",
            
            # 成交量榜 - 按成交量排序
            "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=0&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f5&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152"
        ]
        
        rankings = {}
        ranking_names = ['popularity', 'soaring', 'topic']
        
        for i, url in enumerate(api_urls):
            try:
                print(f"正在获取{ranking_names[i]}数据...")
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    # 清理JSONP响应
                    content = response.text
                    if content.startswith('jQuery'):
                        start = content.find('(') + 1
                        end = content.rfind(')')
                        content = content[start:end]
                    
                    data = json.loads(content)
                    stocks = self._parse_stock_data(data, ranking_names[i])
                    rankings[ranking_names[i]] = stocks
                    print(f"成功获取{ranking_names[i]}: {len(stocks)}只股票")
                else:
                    print(f"获取{ranking_names[i]}失败，状态码: {response.status_code}")
                    rankings[ranking_names[i]] = []
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"获取{ranking_names[i]}数据异常: {e}")
                rankings[ranking_names[i]] = []
        
        return rankings
    
    def _parse_stock_data(self, raw_data: dict, ranking_type: str) -> list:
        """解析股票数据"""
        stocks = []
        
        try:
            if 'data' not in raw_data or 'diff' not in raw_data['data']:
                return stocks
            
            for i, item in enumerate(raw_data['data']['diff']):
                # 基本信息
                stock_code = str(item.get('f12', ''))
                stock_name = str(item.get('f14', ''))
                
                # 跳过无效数据
                if not stock_code or not stock_name or len(stock_code) != 6:
                    continue
                
                # 跳过ST、退市股票
                if any(prefix in stock_name for prefix in ['ST', '*ST', 'PT', '退']):
                    continue
                
                # 价格数据 - 安全转换
                def safe_float(value, default=0.0):
                    try:
                        if value is None or value == '' or value == '-':
                            return default
                        return float(value)
                    except (ValueError, TypeError):
                        return default

                latest_price = safe_float(item.get('f2'))
                change_percent = safe_float(item.get('f3'))
                change_amount = safe_float(item.get('f4'))
                
                # 成交数据 - 安全转换
                def safe_int(value, default=0):
                    try:
                        if value is None or value == '' or value == '-':
                            return default
                        return int(float(value))
                    except (ValueError, TypeError):
                        return default

                volume = safe_int(item.get('f5'))
                turnover = safe_float(item.get('f6'))

                # 其他数据
                popularity = safe_int(item.get('f62'))
                
                # 过滤条件
                if latest_price <= 0:
                    continue
                
                # 飙升榜只要上涨的股票（放宽条件，包含平盘）
                if ranking_type == 'soaring' and change_percent < -0.01:
                    continue
                
                stock = {
                    'rank': i + 1,
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_price': latest_price,
                    'change_percent': change_percent,
                    'change_amount': change_amount,
                    'volume': volume,
                    'turnover': turnover,
                    'amplitude': float(item.get('f7', 0)) if item.get('f7') else 0.0,
                    'turnover_rate': float(item.get('f8', 0)) if item.get('f8') else 0.0,
                    'pe_ratio': float(item.get('f9', 0)) if item.get('f9') else 0.0,
                    'popularity': popularity,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }
                
                stocks.append(stock)
                
                # 限制数量
                if len(stocks) >= 100:
                    break
            
        except Exception as e:
            print(f"解析{ranking_type}数据失败: {e}")
        
        return stocks
    
    def save_data(self, rankings: dict):
        """保存数据到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        ranking_names = {
            'popularity': '人气榜',
            'soaring': '飙升榜', 
            'topic': '话题榜'
        }
        
        for ranking_type, stocks in rankings.items():
            if not stocks:
                print(f"{ranking_names[ranking_type]} 没有数据")
                continue
            
            # 保存CSV
            df = pd.DataFrame(stocks)
            csv_file = f"data/{ranking_type}_{timestamp}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 保存JSON
            json_file = f"data/{ranking_type}_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(stocks, f, ensure_ascii=False, indent=2)
            
            print(f"✅ {ranking_names[ranking_type]}: {len(stocks)}只股票 -> {csv_file}")
    
    def display_data(self, rankings: dict):
        """显示数据"""
        ranking_names = {
            'popularity': '人气榜',
            'soaring': '飙升榜',
            'topic': '话题榜'
        }
        
        for ranking_type, stocks in rankings.items():
            if not stocks:
                continue
                
            print(f"\n{'='*60}")
            print(f"{ranking_names[ranking_type]} (共{len(stocks)}只股票)")
            print(f"{'='*60}")
            print(f"{'排名':<4} {'代码':<8} {'名称':<12} {'最新价':<8} {'涨跌幅':<8} {'成交量':<12}")
            print("-" * 60)
            
            for stock in stocks[:10]:  # 显示前10名
                volume_str = f"{stock['volume']//10000}万" if stock['volume'] >= 10000 else str(stock['volume'])
                print(f"{stock['rank']:<4} {stock['stock_code']:<8} {stock['stock_name']:<12} "
                      f"{stock['latest_price']:<8.2f} {stock['change_percent']:<8.2f}% {volume_str:<12}")
    
    def run(self):
        """运行爬虫"""
        print("🚀 开始获取东方财富榜单数据...")
        print("📊 目标：人气榜100只 + 飙升榜100只 + 话题榜100只")
        print("-" * 60)
        
        # 获取数据
        rankings = self.get_real_stock_data()
        
        # 显示数据
        self.display_data(rankings)
        
        # 保存数据
        self.save_data(rankings)
        
        # 统计
        total_stocks = sum(len(stocks) for stocks in rankings.values())
        print(f"\n🎉 数据获取完成！总计 {total_stocks} 只股票")
        print(f"📁 数据已保存到 data/ 目录")
        
        return rankings

def main():
    """主函数"""
    crawler = SimpleEastMoneyCrawler()
    rankings = crawler.run()
    
    # 显示汇总信息
    print(f"\n📈 汇总信息:")
    for ranking_type, stocks in rankings.items():
        name = {'popularity': '人气榜', 'soaring': '飙升榜', 'topic': '话题榜'}[ranking_type]
        print(f"  {name}: {len(stocks)} 只股票")

if __name__ == "__main__":
    main()
