#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单爬虫主程序
"""

import sys
import os
import argparse
import signal
from datetime import datetime
import pandas as pd

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import RANKING_TYPES, DATA_DIR
from crawler import EastMoneyCrawler
from data_manager import DataManager
from scheduler import get_scheduler, start_scheduler, stop_scheduler
from utils import logger, format_number

class EastMoneyRankingApp:
    """东方财富榜单应用主类"""
    
    def __init__(self):
        self.crawler = EastMoneyCrawler()
        self.data_manager = DataManager()
        self.scheduler = get_scheduler()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("东方财富榜单应用初始化完成")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，正在关闭应用...")
        self.stop()
        sys.exit(0)
    
    def fetch_once(self, ranking_type: str = None, page_size: int = 100):
        """单次获取数据"""
        try:
            logger.info("开始单次数据获取")
            
            if ranking_type:
                # 获取指定榜单
                if ranking_type == 'popularity':
                    data = self.crawler.get_popularity_ranking(page_size=page_size)
                elif ranking_type == 'soaring':
                    data = self.crawler.get_soaring_ranking(page_size=page_size)
                elif ranking_type == 'topic':
                    data = self.crawler.get_topic_ranking(page_size=page_size)
                else:
                    logger.error(f"未知的榜单类型: {ranking_type}")
                    return False
                
                if data:
                    success = self.data_manager.save_ranking_data(ranking_type, data)
                    if success:
                        logger.info(f"{RANKING_TYPES[ranking_type]['name']} 获取成功: {len(data)} 条记录")
                        self.display_ranking(ranking_type, data[:10])  # 显示前10名
                    return success
            else:
                # 获取所有榜单
                rankings = self.crawler.get_all_rankings(page_size=page_size)
                success_count = 0
                total_data_count = 0

                for rtype, data in rankings.items():
                    if data:
                        success = self.data_manager.save_ranking_data(rtype, data)
                        if success:
                            success_count += 1
                            total_data_count += len(data)
                            logger.info(f"{RANKING_TYPES[rtype]['name']} 获取成功: {len(data)} 条记录")
                            self.display_ranking(rtype, data[:5])  # 显示前5名
                    else:
                        logger.warning(f"{RANKING_TYPES[rtype]['name']} 没有获取到数据")

                # 只要有任何一个榜单成功获取数据就算成功
                return success_count > 0 and total_data_count > 0
                
        except Exception as e:
            logger.error(f"单次获取数据失败: {e}")
            return False
    
    def start_daemon(self):
        """启动守护进程模式"""
        try:
            logger.info("启动守护进程模式")
            start_scheduler()
            
            # 保持程序运行
            while True:
                try:
                    import time
                    time.sleep(60)  # 每分钟检查一次
                    
                    # 显示状态信息
                    status = self.scheduler.get_status()
                    if status['is_running']:
                        logger.info(f"调度器运行中，下次更新时间: {status['next_run_time']}")
                    
                except KeyboardInterrupt:
                    break
                    
        except Exception as e:
            logger.error(f"守护进程运行失败: {e}")
        finally:
            self.stop()
    
    def display_ranking(self, ranking_type: str, data: list, limit: int = 10):
        """显示榜单数据"""
        try:
            if not data:
                print(f"\n{RANKING_TYPES[ranking_type]['name']} - 暂无数据")
                return

            print(f"\n{'='*60}")
            print(f"{RANKING_TYPES[ranking_type]['name']} (前{min(limit, len(data))}名)")
            print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*60}")

            # 表头
            print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<12} {'最新价':<8} {'涨跌幅':<8} {'成交量':<12} {'人气值':<10}")
            print("-" * 60)

            # 数据行
            for i, stock in enumerate(data[:limit]):
                rank = i + 1
                code = str(stock.get('stock_code', ''))
                name = str(stock.get('stock_name', ''))[:8]  # 限制名称长度

                # 安全的数值格式化
                try:
                    price = f"{float(stock.get('latest_price', 0)):.2f}"
                except (ValueError, TypeError):
                    price = "0.00"

                try:
                    change = f"{float(stock.get('change_percent', 0)):.2f}%"
                except (ValueError, TypeError):
                    change = "0.00%"

                volume = format_number(stock.get('volume', 0))
                popularity = format_number(stock.get('popularity', 0))

                print(f"{rank:<4} {code:<8} {name:<12} {price:<8} {change:<8} {volume:<12} {popularity:<10}")

            print("-" * 60)

        except Exception as e:
            logger.error(f"显示榜单数据失败: {e}")
    
    def show_statistics(self):
        """显示统计信息"""
        try:
            stats = self.data_manager.get_statistics()
            
            print(f"\n{'='*50}")
            print("数据统计信息")
            print(f"{'='*50}")
            
            for ranking_type, stat in stats.items():
                name = RANKING_TYPES[ranking_type]['name']
                print(f"\n{name}:")
                print(f"  记录数量: {stat['count']}")
                print(f"  平均涨跌幅: {stat['avg_change_percent']:.2f}%")
                print(f"  总成交量: {format_number(stat['total_volume'])}")
                print(f"  最后更新: {stat['last_update']}")
            
            # 调度器状态
            scheduler_status = self.scheduler.get_status()
            print(f"\n调度器状态:")
            print(f"  运行状态: {'运行中' if scheduler_status['is_running'] else '已停止'}")
            print(f"  更新间隔: {scheduler_status['update_interval']} 分钟")
            print(f"  下次更新: {scheduler_status['next_run_time'] or 'N/A'}")
            print(f"  市场状态: {'开市' if scheduler_status['market_open'] else '闭市'}")
            
        except Exception as e:
            logger.error(f"显示统计信息失败: {e}")
    
    def export_data(self, ranking_type: str = None, format_type: str = 'excel'):
        """导出数据"""
        try:
            if format_type == 'excel':
                output_file = self.data_manager.export_to_excel(ranking_type)
                if output_file:
                    print(f"数据已导出到: {output_file}")
                    return True
            else:
                logger.error(f"不支持的导出格式: {format_type}")
                return False
                
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False
    
    def show_latest_data(self, ranking_type: str = None):
        """显示最新数据"""
        try:
            if ranking_type:
                data = self.data_manager.get_latest_ranking(ranking_type)
                self.display_ranking(ranking_type, data, limit=20)
            else:
                for rtype in RANKING_TYPES.keys():
                    data = self.data_manager.get_latest_ranking(rtype)
                    self.display_ranking(rtype, data, limit=10)
                    
        except Exception as e:
            logger.error(f"显示最新数据失败: {e}")
    
    def cleanup_data(self, days: int = 30):
        """清理数据"""
        try:
            self.data_manager.cleanup_old_data(days)
            print(f"已清理 {days} 天前的数据")
        except Exception as e:
            logger.error(f"清理数据失败: {e}")
    
    def stop(self):
        """停止应用"""
        try:
            logger.info("正在停止应用...")
            stop_scheduler()
            if hasattr(self, 'crawler'):
                self.crawler.close()
            logger.info("应用已停止")
        except Exception as e:
            logger.error(f"停止应用失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='东方财富榜单数据爬虫')
    parser.add_argument('--mode', choices=['once', 'daemon', 'show', 'stats', 'export', 'cleanup'], 
                       default='once', help='运行模式')
    parser.add_argument('--ranking', choices=['popularity', 'soaring', 'topic'], 
                       help='指定榜单类型')
    parser.add_argument('--size', type=int, default=100, help='获取数据数量')
    parser.add_argument('--days', type=int, default=30, help='清理数据天数')
    parser.add_argument('--format', choices=['excel', 'csv'], default='excel', help='导出格式')
    
    args = parser.parse_args()
    
    app = EastMoneyRankingApp()
    
    try:
        if args.mode == 'once':
            # 单次获取
            success = app.fetch_once(args.ranking, args.size)
            if success:
                print("数据获取成功")
            else:
                print("数据获取失败")
                
        elif args.mode == 'daemon':
            # 守护进程模式
            app.start_daemon()
            
        elif args.mode == 'show':
            # 显示最新数据
            app.show_latest_data(args.ranking)
            
        elif args.mode == 'stats':
            # 显示统计信息
            app.show_statistics()
            
        elif args.mode == 'export':
            # 导出数据
            success = app.export_data(args.ranking, args.format)
            if not success:
                print("数据导出失败")
                
        elif args.mode == 'cleanup':
            # 清理数据
            app.cleanup_data(args.days)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
    finally:
        app.stop()

if __name__ == "__main__":
    main()
