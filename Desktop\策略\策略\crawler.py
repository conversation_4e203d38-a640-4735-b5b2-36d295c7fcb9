#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单数据爬虫核心模块
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin
import asyncio
import aiohttp

from config import EASTMONEY_CONFIG, API_ENDPOINTS, RANKING_TYPES
from utils import clean_jsonp_response, parse_stock_data, filter_valid_stocks, logger

class EastMoneyCrawler:
    """东方财富数据爬虫"""
    
    def __init__(self):
        self.base_url = EASTMONEY_CONFIG['base_url']
        self.headers = EASTMONEY_CONFIG['headers'].copy()
        self.timeout = EASTMONEY_CONFIG['timeout']
        self.retry_times = EASTMONEY_CONFIG['retry_times']
        self.retry_delay = EASTMONEY_CONFIG['retry_delay']
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        logger.info("东方财富爬虫初始化完成")
    
    def _make_request(self, url: str, params: Dict = None) -> Optional[Dict]:
        """发起HTTP请求"""
        for attempt in range(self.retry_times):
            try:
                logger.debug(f"请求URL: {url}, 参数: {params}")
                
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    # 处理JSONP响应
                    content = clean_jsonp_response(response.text)
                    data = json.loads(content)
                    
                    logger.debug(f"请求成功，数据长度: {len(str(data))}")
                    return data
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求异常 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                if attempt < self.retry_times - 1:
                    time.sleep(self.retry_delay)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                break
            except Exception as e:
                logger.error(f"未知错误: {e}")
                break
        
        return None
    
    def get_popularity_ranking(self, page: int = 1, page_size: int = 100) -> List[Dict]:
        """获取人气榜数据"""
        logger.info(f"获取人气榜数据 - 页码: {page}, 每页: {page_size}")
        
        try:
            # 使用热门股票API，按人气排序
            endpoint = API_ENDPOINTS['hot_stocks']
            url = urljoin(self.base_url, endpoint['url'])
            
            params = endpoint['params'].copy()
            params.update({
                'pn': page,
                'pz': page_size,
                'fid': 'f62',  # 按人气值排序
                'po': 0,       # 降序
            })
            
            raw_data = self._make_request(url, params)
            if raw_data:
                stocks = parse_stock_data(raw_data)
                # 过滤出有效的活跃股票
                valid_stocks = filter_valid_stocks(stocks)
                logger.info(f"成功获取人气榜数据: {len(valid_stocks)} 只有效股票")
                return valid_stocks
            
        except Exception as e:
            logger.error(f"获取人气榜数据失败: {e}")
        
        return []
    
    def get_soaring_ranking(self, page: int = 1, page_size: int = 100) -> List[Dict]:
        """获取飙升榜数据(涨幅榜)"""
        logger.info(f"获取飙升榜数据 - 页码: {page}, 每页: {page_size}")
        
        try:
            # 使用涨幅榜API
            endpoint = API_ENDPOINTS['gain_ranking']
            url = urljoin(self.base_url, endpoint['url'])
            
            params = endpoint['params'].copy()
            params.update({
                'pn': page,
                'pz': page_size,
                'fid': 'f3',   # 按涨跌幅排序
                'po': 0,       # 降序
            })
            
            raw_data = self._make_request(url, params)
            if raw_data:
                stocks = parse_stock_data(raw_data)
                # 过滤出有效的活跃股票
                valid_stocks = filter_valid_stocks(stocks)
                # 过滤掉涨幅为负的股票
                rising_stocks = [s for s in valid_stocks if s.get('change_percent', 0) > 0]
                logger.info(f"成功获取飙升榜数据: {len(rising_stocks)} 只上涨股票")
                return rising_stocks
            
        except Exception as e:
            logger.error(f"获取飙升榜数据失败: {e}")
        
        return []
    
    def get_topic_ranking(self, page: int = 1, page_size: int = 100) -> List[Dict]:
        """获取话题榜数据(成交量活跃榜)"""
        logger.info(f"获取话题榜数据 - 页码: {page}, 每页: {page_size}")

        try:
            # 使用成交量排序作为话题榜
            endpoint = API_ENDPOINTS['volume_ranking']
            url = urljoin(self.base_url, endpoint['url'])

            params = endpoint['params'].copy()
            params.update({
                'pn': page,
                'pz': page_size,
            })

            raw_data = self._make_request(url, params)
            if raw_data:
                stocks = parse_stock_data(raw_data)
                # 过滤出有效的活跃股票
                valid_stocks = filter_valid_stocks(stocks)
                logger.info(f"成功获取话题榜数据: {len(valid_stocks)} 只有效股票")
                return valid_stocks

        except Exception as e:
            logger.error(f"获取话题榜数据失败: {e}")

        return []
    
    def get_all_rankings(self, page_size: int = 100) -> Dict[str, List[Dict]]:
        """获取所有榜单数据"""
        logger.info("开始获取所有榜单数据")
        
        results = {}
        
        # 获取人气榜
        results['popularity'] = self.get_popularity_ranking(page_size=page_size)
        time.sleep(1)  # 避免请求过快
        
        # 获取飙升榜
        results['soaring'] = self.get_soaring_ranking(page_size=page_size)
        time.sleep(1)
        
        # 获取话题榜
        results['topic'] = self.get_topic_ranking(page_size=page_size)
        
        logger.info(f"所有榜单数据获取完成: "
                   f"人气榜 {len(results['popularity'])} 只, "
                   f"飙升榜 {len(results['soaring'])} 只, "
                   f"话题榜 {len(results['topic'])} 只")
        
        return results
    
    def get_stock_detail(self, stock_code: str) -> Optional[Dict]:
        """获取单只股票详细信息"""
        try:
            # 构建股票详情API
            url = urljoin(self.base_url, '/api/qt/stock/get')
            params = {
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'invt': 2,
                'fltt': 2,
                'secid': f"1.{stock_code}" if stock_code.startswith('6') else f"0.{stock_code}",
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25',
                'cb': 'jQuery'
            }
            
            raw_data = self._make_request(url, params)
            if raw_data and 'data' in raw_data:
                return raw_data['data']
                
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 详情失败: {e}")
        
        return None
    
    async def get_rankings_async(self, page_size: int = 100) -> Dict[str, List[Dict]]:
        """异步获取所有榜单数据"""
        logger.info("开始异步获取所有榜单数据")
        
        async with aiohttp.ClientSession(headers=self.headers, timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            tasks = []
            
            # 创建异步任务
            tasks.append(self._get_ranking_async(session, 'popularity', page_size))
            tasks.append(self._get_ranking_async(session, 'soaring', page_size))
            tasks.append(self._get_ranking_async(session, 'topic', page_size))
            
            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            rankings = {}
            ranking_types = ['popularity', 'soaring', 'topic']
            
            for i, result in enumerate(results):
                ranking_type = ranking_types[i]
                if isinstance(result, Exception):
                    logger.error(f"异步获取 {ranking_type} 榜单失败: {result}")
                    rankings[ranking_type] = []
                else:
                    rankings[ranking_type] = result or []
            
            logger.info(f"异步获取完成: "
                       f"人气榜 {len(rankings['popularity'])} 只, "
                       f"飙升榜 {len(rankings['soaring'])} 只, "
                       f"话题榜 {len(rankings['topic'])} 只")
            
            return rankings
    
    async def _get_ranking_async(self, session: aiohttp.ClientSession, ranking_type: str, page_size: int) -> List[Dict]:
        """异步获取单个榜单数据"""
        try:
            if ranking_type == 'popularity':
                endpoint = API_ENDPOINTS['hot_stocks']
                fid = 'f62'  # 人气值
            elif ranking_type == 'soaring':
                endpoint = API_ENDPOINTS['gain_ranking']
                fid = 'f3'   # 涨跌幅
            else:  # topic
                endpoint = API_ENDPOINTS['stock_list']
                fid = 'f5'   # 成交量
            
            url = urljoin(self.base_url, endpoint['url'])
            params = endpoint['params'].copy()
            params.update({
                'pn': 1,
                'pz': page_size,
                'fid': fid,
                'po': 0,
            })
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    text = await response.text()
                    content = clean_jsonp_response(text)
                    data = json.loads(content)
                    stocks = parse_stock_data(data)
                    
                    # 飙升榜过滤负涨幅
                    if ranking_type == 'soaring':
                        stocks = [s for s in stocks if s.get('change_percent', 0) > 0]
                    
                    return stocks
                    
        except Exception as e:
            logger.error(f"异步获取 {ranking_type} 榜单失败: {e}")
        
        return []
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()
            logger.info("爬虫会话已关闭")
