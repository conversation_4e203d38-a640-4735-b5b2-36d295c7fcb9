#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单爬虫配置文件
"""

import os
from datetime import datetime

# 基础配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
LOGS_DIR = os.path.join(BASE_DIR, 'logs')

# 创建必要的目录
for directory in [DATA_DIR, LOGS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# 东方财富API配置
EASTMONEY_CONFIG = {
    'base_url': 'https://push2.eastmoney.com',
    'datacenter_url': 'https://datacenter-web.eastmoney.com',
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://vipmoney.eastmoney.com/',
    },
    'timeout': 30,
    'retry_times': 3,
    'retry_delay': 2,
}

# 榜单类型配置
RANKING_TYPES = {
    'popularity': {
        'name': '人气榜',
        'description': '股票人气排行榜',
        'file_prefix': 'popularity',
    },
    'soaring': {
        'name': '飙升榜', 
        'description': '股票涨幅排行榜',
        'file_prefix': 'soaring',
    },
    'topic': {
        'name': '话题榜',
        'description': '热门话题股票榜',
        'file_prefix': 'topic',
    }
}

# API端点配置
API_ENDPOINTS = {
    # 股票列表API (支持多种排序)
    'stock_list': {
        'url': '/api/qt/clist/get',
        'params': {
            'cb': 'jQuery',
            'pn': 1,  # 页码
            'pz': 50,  # 每页数量
            'po': 1,   # 排序方向 1升序 0降序
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
        }
    },
    
    # 热门股票API
    'hot_stocks': {
        'url': '/api/qt/clist/get',
        'params': {
            'cb': 'jQuery',
            'pn': 1,
            'pz': 50,
            'po': 0,  # 降序
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fid': 'f62',  # 按人气排序
            'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',  # 股票类型过滤
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
        }
    },
    
    # 涨幅榜API
    'gain_ranking': {
        'url': '/api/qt/clist/get',
        'params': {
            'cb': 'jQuery',
            'pn': 1,
            'pz': 50,
            'po': 0,  # 降序
            'np': 1,
            'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
            'fltt': 2,
            'invt': 2,
            'fid': 'f3',  # 按涨跌幅排序
            'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
            'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
        }
    }
}

# 字段映射配置
FIELD_MAPPING = {
    'f1': 'unknown1',
    'f2': 'latest_price',      # 最新价
    'f3': 'change_percent',    # 涨跌幅
    'f4': 'change_amount',     # 涨跌额
    'f5': 'volume',            # 成交量
    'f6': 'turnover',          # 成交额
    'f7': 'amplitude',         # 振幅
    'f8': 'turnover_rate',     # 换手率
    'f9': 'pe_ratio',          # 市盈率
    'f10': 'volume_ratio',     # 量比
    'f11': 'unknown11',
    'f12': 'stock_code',       # 股票代码
    'f13': 'unknown13',
    'f14': 'stock_name',       # 股票名称
    'f15': 'highest_price',    # 最高价
    'f16': 'lowest_price',     # 最低价
    'f17': 'open_price',       # 开盘价
    'f18': 'prev_close',       # 昨收价
    'f20': 'total_market_cap', # 总市值
    'f21': 'circulating_market_cap', # 流通市值
    'f22': 'speed',            # 涨速
    'f23': 'pb_ratio',         # 市净率
    'f24': 'unknown24',
    'f25': 'unknown25',
    'f62': 'popularity',       # 人气值
    'f115': 'unknown115',
    'f128': 'unknown128',
    'f136': 'unknown136',
    'f152': 'unknown152',
}

# 数据存储配置
DATA_CONFIG = {
    'save_formats': ['csv', 'json'],  # 支持的保存格式
    'history_days': 30,               # 保留历史数据天数
    'backup_enabled': True,           # 是否启用备份
    'max_records_per_file': 10000,    # 单文件最大记录数
}

# 定时任务配置
SCHEDULE_CONFIG = {
    'update_interval': 5,      # 更新间隔(分钟)
    'market_hours': {          # 交易时间
        'start': '09:30',
        'end': '15:00',
        'lunch_start': '11:30',
        'lunch_end': '13:00',
    },
    'weekend_update': False,   # 周末是否更新
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

def get_current_timestamp():
    """获取当前时间戳"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def get_data_filename(ranking_type, file_format='csv'):
    """生成数据文件名"""
    timestamp = datetime.now().strftime('%Y%m%d')
    prefix = RANKING_TYPES.get(ranking_type, {}).get('file_prefix', ranking_type)
    return f"{prefix}_{timestamp}.{file_format}"

def get_history_filename(ranking_type):
    """生成历史数据文件名"""
    prefix = RANKING_TYPES.get(ranking_type, {}).get('file_prefix', ranking_type)
    return f"{prefix}_history.json"
