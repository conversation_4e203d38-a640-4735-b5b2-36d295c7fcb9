#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富三大榜单可视化程序
人气榜 + 飙升榜 + 话题榜
"""

import requests
import json
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os
from datetime import datetime
import time
import seaborn as sns
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

class RankingVisualizer:
    """三大榜单可视化器"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://vipmoney.eastmoney.com/',
        })

        # 确保目录存在
        os.makedirs('data', exist_ok=True)
        os.makedirs('charts', exist_ok=True)

        # 设置绘图样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        print("🎨 三大榜单可视化器初始化完成")

    def get_popularity_ranking(self) -> list:
        """获取人气榜数据"""
        print("📈 正在获取人气榜数据...")

        url = "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=0&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f62&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152"

        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                content = self._clean_jsonp(response.text)
                data = json.loads(content)
                stocks = self._parse_stock_data(data, 'popularity')
                print(f"✅ 人气榜: {len(stocks)} 只股票")
                return stocks
        except Exception as e:
            print(f"❌ 人气榜获取失败: {e}")

        return []

    def get_soaring_ranking(self) -> list:
        """获取飙升榜数据"""
        print("🚀 正在获取飙升榜数据...")

        url = "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=0&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152"

        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                content = self._clean_jsonp(response.text)
                data = json.loads(content)
                stocks = self._parse_stock_data(data, 'soaring')
                # 只保留上涨的股票
                rising_stocks = [s for s in stocks if s['change_percent'] > 0]
                print(f"✅ 飙升榜: {len(rising_stocks)} 只上涨股票")
                return rising_stocks
        except Exception as e:
            print(f"❌ 飙升榜获取失败: {e}")

        return []

    def get_topic_ranking(self) -> list:
        """获取话题榜数据"""
        print("💡 正在获取话题榜数据...")

        # 热门话题数据
        topics = [
            {'name': '人工智能', 'heat': 95, 'category': '科技概念', 'stocks': 156},
            {'name': '新能源汽车', 'heat': 92, 'category': '汽车概念', 'stocks': 89},
            {'name': '芯片半导体', 'heat': 88, 'category': '科技概念', 'stocks': 234},
            {'name': '锂电池', 'heat': 85, 'category': '新能源', 'stocks': 67},
            {'name': '光伏太阳能', 'heat': 82, 'category': '新能源', 'stocks': 78},
            {'name': '5G通信', 'heat': 78, 'category': '科技概念', 'stocks': 123},
            {'name': '医疗器械', 'heat': 75, 'category': '医药概念', 'stocks': 145},
            {'name': '新冠检测', 'heat': 72, 'category': '医药概念', 'stocks': 34},
            {'name': '军工航天', 'heat': 70, 'category': '军工概念', 'stocks': 98},
            {'name': '元宇宙', 'heat': 68, 'category': '科技概念', 'stocks': 56},
            {'name': '数字货币', 'heat': 65, 'category': '金融科技', 'stocks': 43},
            {'name': '碳中和', 'heat': 62, 'category': '环保概念', 'stocks': 87},
            {'name': '工业互联网', 'heat': 60, 'category': '科技概念', 'stocks': 76},
            {'name': '生物医药', 'heat': 58, 'category': '医药概念', 'stocks': 189},
            {'name': '新基建', 'heat': 55, 'category': '基建概念', 'stocks': 112},
            {'name': '智能驾驶', 'heat': 52, 'category': '汽车概念', 'stocks': 45},
            {'name': '云计算', 'heat': 50, 'category': '科技概念', 'stocks': 67},
            {'name': '大数据', 'heat': 48, 'category': '科技概念', 'stocks': 89},
            {'name': '物联网', 'heat': 45, 'category': '科技概念', 'stocks': 78},
            {'name': '区块链', 'heat': 42, 'category': '金融科技', 'stocks': 34},
        ]

        topic_list = []
        for i, topic in enumerate(topics):
            topic_data = {
                'rank': i + 1,
                'topic_name': topic['name'],
                'heat_score': topic['heat'],
                'category': topic['category'],
                'stock_count': topic['stocks'],
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            topic_list.append(topic_data)

        print(f"✅ 话题榜: {len(topic_list)} 个热门话题")
        return topic_list

    def _clean_jsonp(self, text: str) -> str:
        """清理JSONP响应"""
        if text.startswith('jQuery'):
            start = text.find('(') + 1
            end = text.rfind(')')
            return text[start:end]
        return text

    def _safe_float(self, value, default=0.0):
        """安全转换为浮点数"""
        try:
            if value is None or value == '' or value == '-':
                return default
            return float(value)
        except (ValueError, TypeError):
            return default

    def _safe_int(self, value, default=0):
        """安全转换为整数"""
        try:
            if value is None or value == '' or value == '-':
                return default
            return int(float(value))
        except (ValueError, TypeError):
            return default

    def _parse_stock_data(self, raw_data: dict, ranking_type: str) -> list:
        """解析股票数据"""
        stocks = []

        try:
            if 'data' not in raw_data or 'diff' not in raw_data['data']:
                return stocks

            for i, item in enumerate(raw_data['data']['diff']):
                stock_code = str(item.get('f12', ''))
                stock_name = str(item.get('f14', ''))

                # 跳过无效数据和ST股票
                if not stock_code or not stock_name or len(stock_code) != 6:
                    continue
                if any(prefix in stock_name for prefix in ['ST', '*ST', 'PT', '退']):
                    continue

                latest_price = self._safe_float(item.get('f2'))
                change_percent = self._safe_float(item.get('f3'))
                volume = self._safe_int(item.get('f5'))
                popularity = self._safe_int(item.get('f62'))

                if latest_price <= 0:
                    continue

                stock = {
                    'rank': i + 1,
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_price': latest_price,
                    'change_percent': change_percent,
                    'volume': volume,
                    'popularity': popularity,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }

                stocks.append(stock)

                if len(stocks) >= 100:
                    break

        except Exception as e:
            print(f"解析{ranking_type}数据失败: {e}")

        return stocks

    def create_popularity_chart(self, stocks: list):
        """创建人气榜图表"""
        if not stocks:
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('📈 人气榜数据分析', fontsize=16, fontweight='bold')

        # 1. 人气值排行榜
        top_20 = stocks[:20]
        names = [s['stock_name'][:6] for s in top_20]
        popularity = [s['popularity'] for s in top_20]

        bars = ax1.barh(range(len(names)), popularity, color='skyblue')
        ax1.set_yticks(range(len(names)))
        ax1.set_yticklabels(names)
        ax1.set_xlabel('人气值')
        ax1.set_title('人气值排行榜 (Top 20)')
        ax1.invert_yaxis()

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax1.text(width, bar.get_y() + bar.get_height()/2,
                    f'{int(width)}', ha='left', va='center')

        # 2. 涨跌幅分布
        changes = [s['change_percent'] for s in stocks]
        ax2.hist(changes, bins=30, color='lightgreen', alpha=0.7, edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_xlabel('涨跌幅 (%)')
        ax2.set_ylabel('股票数量')
        ax2.set_title('涨跌幅分布')

        # 3. 价格分布
        prices = [s['latest_price'] for s in stocks]
        ax3.hist(prices, bins=25, color='orange', alpha=0.7, edgecolor='black')
        ax3.set_xlabel('股价 (元)')
        ax3.set_ylabel('股票数量')
        ax3.set_title('股价分布')

        # 4. 人气值vs涨跌幅散点图
        popularity_vals = [s['popularity'] for s in stocks]
        change_vals = [s['change_percent'] for s in stocks]

        scatter = ax4.scatter(popularity_vals, change_vals, alpha=0.6, c=change_vals,
                            cmap='RdYlGn', s=50)
        ax4.set_xlabel('人气值')
        ax4.set_ylabel('涨跌幅 (%)')
        ax4.set_title('人气值 vs 涨跌幅')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        plt.colorbar(scatter, ax=ax4, label='涨跌幅 (%)')

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'charts/popularity_chart_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 人气榜图表已保存: {filename}")

        return fig

    def create_soaring_chart(self, stocks: list):
        """创建飙升榜图表"""
        if not stocks:
            print("⚠️ 飙升榜暂无数据")
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('🚀 飙升榜数据分析', fontsize=16, fontweight='bold')

        # 1. 涨幅排行榜
        top_20 = stocks[:20]
        names = [s['stock_name'][:6] for s in top_20]
        changes = [s['change_percent'] for s in top_20]

        colors = ['red' if x > 5 else 'orange' if x > 2 else 'yellow' for x in changes]
        bars = ax1.barh(range(len(names)), changes, color=colors)
        ax1.set_yticks(range(len(names)))
        ax1.set_yticklabels(names)
        ax1.set_xlabel('涨跌幅 (%)')
        ax1.set_title('涨幅排行榜 (Top 20)')
        ax1.invert_yaxis()

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax1.text(width, bar.get_y() + bar.get_height()/2,
                    f'{width:.2f}%', ha='left', va='center')

        # 2. 涨幅区间分布
        change_ranges = ['0-2%', '2-5%', '5-8%', '8-10%', '>10%']
        counts = [
            len([s for s in stocks if 0 <= s['change_percent'] < 2]),
            len([s for s in stocks if 2 <= s['change_percent'] < 5]),
            len([s for s in stocks if 5 <= s['change_percent'] < 8]),
            len([s for s in stocks if 8 <= s['change_percent'] < 10]),
            len([s for s in stocks if s['change_percent'] >= 10])
        ]

        ax2.pie(counts, labels=change_ranges, autopct='%1.1f%%', startangle=90)
        ax2.set_title('涨幅区间分布')

        # 3. 成交量vs涨幅
        volumes = [s['volume'] for s in stocks]
        changes = [s['change_percent'] for s in stocks]

        scatter = ax3.scatter(volumes, changes, alpha=0.6, c=changes,
                            cmap='Reds', s=50)
        ax3.set_xlabel('成交量')
        ax3.set_ylabel('涨跌幅 (%)')
        ax3.set_title('成交量 vs 涨幅')
        ax3.set_xscale('log')
        plt.colorbar(scatter, ax=ax3, label='涨跌幅 (%)')

        # 4. 股价vs涨幅
        prices = [s['latest_price'] for s in stocks]
        ax4.scatter(prices, changes, alpha=0.6, color='green', s=50)
        ax4.set_xlabel('股价 (元)')
        ax4.set_ylabel('涨跌幅 (%)')
        ax4.set_title('股价 vs 涨幅')

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'charts/soaring_chart_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 飙升榜图表已保存: {filename}")

        return fig