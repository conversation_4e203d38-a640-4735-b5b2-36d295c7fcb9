#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单网站分析工具
分析目标网站的API接口和数据结构
"""

import requests
import json
import time
from urllib.parse import urljoin, urlparse
import re

class EastMoneyAnalyzer:
    def __init__(self):
        self.base_url = "https://vipmoney.eastmoney.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def find_api_endpoints(self):
        """查找可能的API端点"""
        print("正在分析东方财富榜单API...")
        
        # 常见的API路径模式
        potential_apis = [
            "/collect/app_ranking/ranking/api/stock/popularity",
            "/collect/app_ranking/ranking/api/stock/soaring", 
            "/collect/app_ranking/ranking/api/stock/topic",
            "/api/ranking/stock/popularity",
            "/api/ranking/stock/soaring",
            "/api/ranking/stock/topic",
            "/ranking/api/stock",
            "/api/stock/ranking",
        ]
        
        working_apis = []
        
        for api_path in potential_apis:
            try:
                url = urljoin(self.base_url, api_path)
                print(f"测试API: {url}")
                
                response = self.session.get(url, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        working_apis.append({
                            'url': url,
                            'status': response.status_code,
                            'data_sample': str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                        })
                        print(f"✓ 找到有效API: {url}")
                        print(f"数据样本: {str(data)[:100]}...")
                    except json.JSONDecodeError:
                        print(f"响应不是JSON格式")
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"请求失败: {e}")
        
        return working_apis
    
    def analyze_main_page(self):
        """分析主页面，寻找API调用"""
        print("\n正在分析主页面...")
        
        try:
            main_url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html"
            response = self.session.get(main_url, timeout=10)
            
            if response.status_code == 200:
                content = response.text
                
                # 查找JavaScript中的API调用
                api_patterns = [
                    r'fetch\(["\']([^"\']+)["\']',
                    r'axios\.get\(["\']([^"\']+)["\']',
                    r'\.get\(["\']([^"\']+)["\']',
                    r'url:\s*["\']([^"\']+)["\']',
                    r'api["\']:\s*["\']([^"\']+)["\']',
                ]
                
                found_apis = set()
                for pattern in api_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if 'api' in match.lower() or 'ranking' in match.lower():
                            found_apis.add(match)
                
                print(f"在页面中找到可能的API: {found_apis}")
                return list(found_apis)
                
        except Exception as e:
            print(f"分析主页面失败: {e}")
        
        return []
    
    def test_common_endpoints(self):
        """测试常见的东方财富API端点"""
        print("\n测试常见的东方财富API端点...")
        
        # 基于东方财富网站的常见API模式
        test_urls = [
            "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152",
            "https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=TRADE_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPT_LICO_FU_STATIS&columns=SECUCODE%2CSECURITY_CODE%2CSECURITY_NAME_ABBR%2CTRADE_DATE%2CCLOSE_PRICE%2CCHANGE_RATE",
            "https://datacenter-web.eastmoney.com/api/data/get?type=RPTA_WEB_HOTRANK&sty=SECUCODE,SECURITY_CODE,SECURITY_NAME_ABBR,CHANGE_RATE,CLOSE_PRICE,TRADE_DATE&filter=&p=1&ps=50&sr=-1&st=CHANGE_RATE",
            "https://datacenter-web.eastmoney.com/api/data/get?type=RPTA_WEB_HOTRANK&sty=SECUCODE,SECURITY_CODE,SECURITY_NAME_ABBR,CHANGE_RATE,CLOSE_PRICE,TRADE_DATE,HOT_RANK&filter=&p=1&ps=50&sr=-1&st=HOT_RANK",
        ]
        
        working_endpoints = []
        
        for url in test_urls:
            try:
                print(f"测试: {url[:80]}...")
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        # 处理JSONP响应
                        content = response.text
                        if content.startswith('jQuery'):
                            # 提取JSONP中的JSON数据
                            start = content.find('(') + 1
                            end = content.rfind(')')
                            json_str = content[start:end]
                            data = json.loads(json_str)
                        else:
                            data = response.json()
                        
                        working_endpoints.append({
                            'url': url,
                            'data_keys': list(data.keys()) if isinstance(data, dict) else 'non-dict',
                            'sample': str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                        })
                        print(f"✓ 成功获取数据")
                        
                    except Exception as e:
                        print(f"解析响应失败: {e}")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"请求失败: {e}")
        
        return working_endpoints

def main():
    analyzer = EastMoneyAnalyzer()
    
    print("=== 东方财富榜单API分析 ===")
    
    # 1. 分析主页面
    page_apis = analyzer.analyze_main_page()
    
    # 2. 查找API端点
    api_endpoints = analyzer.find_api_endpoints()
    
    # 3. 测试常见端点
    common_endpoints = analyzer.test_common_endpoints()
    
    # 保存分析结果
    results = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'page_apis': page_apis,
        'api_endpoints': api_endpoints,
        'common_endpoints': common_endpoints
    }
    
    with open('api_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 分析完成 ===")
    print(f"找到 {len(page_apis)} 个页面API")
    print(f"找到 {len(api_endpoints)} 个有效API端点")
    print(f"找到 {len(common_endpoints)} 个可用的常见端点")
    print("详细结果已保存到 api_analysis_results.json")

if __name__ == "__main__":
    main()
